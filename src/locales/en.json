{"common": {"confirm": "Confirm", "cancel": "Cancel", "save": "Save", "edit": "Edit", "delete": "Delete", "add": "Add", "create": "Create", "update": "Update", "search": "Search", "filter": "Filter", "reset": "Reset", "submit": "Submit", "back": "Back", "next": "Next", "previous": "Previous", "close": "Close", "open": "Open", "view": "View", "download": "Download", "upload": "Upload", "import": "Import", "export": "Export", "refresh": "Refresh", "reload": "Reload", "clear": "Clear", "select": "Select", "selectAll": "Select All", "deselectAll": "Deselect All", "enable": "Enable", "disable": "Disable", "enabled": "Enabled", "disabled": "Disabled", "active": "Active", "inactive": "Inactive", "online": "Online", "offline": "Offline", "connected": "Connected", "disconnected": "Disconnected", "loading": "Loading...", "success": "Success", "error": "Error", "warning": "Warning", "info": "Info", "yes": "Yes", "no": "No", "ok": "OK", "actions": "Actions", "retry": "Retry", "status": "Status", "created": "Created", "updated": "Updated", "more": "more", "on": "ON", "off": "OFF", "show": "Show", "hide": "<PERSON>de", "unknown": "Unknown", "noDescription": "No description", "auto": "Auto", "manual": "Manual", "bytes": "bytes", "items": "items", "total": "Total", "of": "of", "page": "Page", "pages": "Pages", "item": "<PERSON><PERSON>", "selected": "Selected", "all": "All", "none": "None"}, "languages": {"chinese": "Chinese", "english": "English"}, "knowledge": {"title": "Knowledge Base", "createKnowledge": "Create Knowledge Base", "knowledgeName": "Knowledge Base Name", "description": "Description", "enterName": "Enter knowledge base name", "enterDescription": "Enter description (optional)", "nameRequired": "Please enter knowledge base name", "createSuccess": "Knowledge base created successfully!", "deleteConfirm": "Are you sure you want to delete \"{{name}}\"? This action cannot be undone.", "deleteSuccess": "Knowledge base deleted successfully!", "dataset": "Dataset", "setting": "Settings", "retrievalTesting": "Retrieval Testing", "chunks": "Chunks", "createFailed": "Failed to create knowledge base", "searchPlaceholder": "Search knowledge bases...", "createFirstKnowledge": "Create Your First Knowledge Base", "searchAllPlaceholder": "Search knowledge base, documents...", "deleteTitle": "Delete Knowledge Base", "totalKnowledgeBases": "Total Knowledge Bases", "noKnowledgeBases": "No knowledge bases found", "noMoreKnowledgeBases": "No more knowledge bases to load", "viewDocuments": "View Documents", "manageDescription": "Manage your knowledge repositories and documents", "totalDocuments": "Total Documents", "totalChunks": "Total Chunks", "documents": "Documents", "noDescription": "No description provided", "docsCount": "docs", "chunksCount": "chunks", "loadingSettings": "Loading knowledge base settings...", "settingsTitle": "Knowledge Base Settings", "settingsDescription": "Configure your knowledge base properties and processing settings", "generalSettings": "General Settings", "knowledgeBaseName": "Knowledge Base Name", "nameMaxLength": "Name cannot exceed 100 characters", "namePlaceholder": "Enter knowledge base name", "language": "Language", "languageRequired": "Please select language", "languagePlaceholder": "Select language", "permission": "Permission", "permissionRequired": "Please select permission", "permissionPlaceholder": "Select permission", "permissionPrivate": "Private (Only me)", "permissionTeam": "Team", "permissionPublic": "Public", "pdfParser": "PDF Parser", "pdfParserRequired": "Please select PDF parser", "pdfParserPlaceholder": "Select PDF parser", "parserType": "Parser Type", "parserTypeRequired": "Please select parser type", "parserTypePlaceholder": "Select parser type", "selectLanguage": "Select language", "descriptionMaxLength": "Description cannot exceed 500 characters", "descriptionPlaceholder": "Enter description (optional)", "processingSettings": "Processing Settings", "embedModel": "Embedding Model", "embedModelRequired": "Please select embedding model", "embedModelTooltip": "The default embedding model used by the knowledge base. Once text chunks have been generated within the knowledge base, you will not be able to change the default embedding model unless you delete all text chunks within the knowledge base.", "selectModel": "Select Model", "noModelsAvailable": "No models available", "dangerZone": "Danger Zone", "deleteKnowledgeBase": "Delete Knowledge Base", "deleteWarning": "Once you delete a knowledge base, there is no going back. Please be certain.", "updateSuccess": "Knowledge base updated successfully!", "updateFailed": "Failed to update knowledge base", "deleteConfirmMessage": "Are you sure you want to delete this knowledge base?", "deleteCannotUndo": "This action cannot be undone.", "deleteAllData": "All documents and data will be permanently removed.", "deleteFailed": "Failed to delete knowledge base", "parserDescriptions": {"naive": {"title": "General", "description": "Supported file formats are MD, MDX, DOCX, XLSX, XLS (Excel 97-2003), PPT, PDF, TXT, JPEG, JPG, PNG, TIF, GIF, CSV, JSON, EML, HTML.\\n\\nThis method chunks files using a 'naive' method:\\n• Use vision detection model to split the texts into smaller segments.\\n• Then, combine adjacent segments until the token count exceeds the threshold specified by 'Chunk token number for text', at which point a chunk is created."}, "qa": {"title": "Q&A", "description": "This chunking method supports XLSX and CSV/TXT file formats.\\n\\n• If the file is in XLSX or XLS (Excel 97-2003) format, it should contain two columns with no headers: one for questions and another for answers, with the question column preceding the answer column. Multiple worksheets are acceptable, provided the column structure is correct.\\n• If the file is in CSV/TXT format, it must be UTF-8 encoded and use TAB as the delimiter to separate questions and answers."}, "resume": {"title": "Resume", "description": "Only PDF format is supported.\\n\\nWe assume that resumes have a hierarchical section structure, using the lowest-level section headings as the basic unit for document chunking. Therefore, charts and tables within the same section will not be separated, which may result in larger chunk sizes."}, "manual": {"title": "Manual", "description": "Only PDF format is supported.\\n\\nWe assume that manuals have a hierarchical section structure, using the lowest-level section headings as the basic unit for document chunking. Therefore, charts and tables within the same section will not be separated, which may result in larger chunk sizes."}, "table": {"title": "Table", "description": "Supported file formats are XLSX, XLS (Excel 97-2003), CSV.\\n\\nThis method is specifically designed for tabular data. Each row in the table is treated as a separate chunk, preserving the structure and relationships within the data."}, "paper": {"title": "Paper", "description": "Only PDF file is supported.\\n\\nPapers will be split by section, such as abstract, 1.1, 1.2. This approach enables the LLM to summarize the paper more effectively and to provide more comprehensive, understandable responses. However, it also increases the context for AI conversations and adds to the computational cost for the LLM. So during a conversation, consider reducing the value of 'topN'."}, "book": {"title": "Book", "description": "Only PDF is supported.\\n\\nBooks will be split by chapter or section. This method is optimized for long-form content with hierarchical structure, ensuring that related content stays together while maintaining reasonable chunk sizes."}, "laws": {"title": "Laws", "description": "Supported file formats are DOCX,PDF,TXT. \\n     Legal documents typically follow a rigorous writing format. We use text feature to identify split point. \\n      The chunk has a granularity consistent with 'ARTICLE', ensuring all upper level text is included in the chunk."}, "presentation": {"title": "Presentation", "description": "Supported file formats are PDF, PPTX.\\n\\nEvery page in the slides is treated as a chunk, with its thumbnail image stored. This chunking method is automatically applied to all uploaded PPT files, so you do not need to specify it manually."}, "picture": {"title": "Picture", "description": "Supported file formats are JPEG, JPG, PNG, TIF, GIF, PDF.\\n\\nImages will be processed using vision models to extract text and visual information. Each image is treated as a separate chunk with both visual and textual content preserved."}, "one": {"title": "One", "description": "Supported file formats are MD, DOCX, PDF, TXT.\\n\\nThe entire document is treated as a single chunk. Applicable when you require the LLM to summarize the entire document, provided it can handle that amount of context length."}, "audio": {"title": "Audio", "description": "Supported file formats are MP3, WAV, M4A, AAC.\\n\\nAudio files will be transcribed to text using speech-to-text models, then chunked based on natural speech pauses and content boundaries."}, "email": {"title": "Email", "description": "Supported file formats are EML, MSG.\\n\\nEmail messages will be parsed to extract headers, body content, and attachments. Each email is typically treated as a separate chunk while preserving the email structure and metadata."}, "tag": {"title": "Tag", "description": "A knowledge base using the 'Tag' chunking method functions as a tag set. Other knowledge bases can use it to tag their own chunks, and queries to these knowledge bases will also be tagged using this tag set.\\n\\nKnowledge base using 'Tag' as a chunking method will NOT be involved in a Retrieval-Augmented Generation (RAG) process. Each chunk in this knowledge base is an independent description-tag pair.\\n\\nSupported file formats include XLSX and CSV/TXT:\\n• If a file is in XLSX format, it should contain two columns without headers: one for tag descriptions and the other for tag names, with the Description column preceding the Tag column.\\n• If a file is in CSV/TXT format, it must be UTF-8 encoded with TAB as the delimiter to separate descriptions and tags.\\n• In a Tag column, comma is used to separate tags."}, "knowledge_graph": {"title": "Knowledge Graph", "description": "Supported file formats are DOCX, EXCEL, PPT, IMAGE, PDF, TXT, MD, JSON, EML.\\n\\nThis approach chunks files using the 'naive'/'General' method. It splits a document into segments and then combines adjacent segments until the token count exceeds the threshold specified by 'Chunk token number for text', at which point a chunk is created.\\n\\nThe chunks are then fed to the LLM to extract entities and relationships for a knowledge graph and a mind map. Ensure that you set the Entity types."}}, "backToKnowledgeBase": "Back to Knowledge Base", "created": "Created", "manageDocuments": "Manage documents in your knowledge base", "searchDocuments": "Search documents...", "batchParse": "<PERSON><PERSON> Parse", "batchDelete": "<PERSON><PERSON> Delete", "refresh": "Refresh", "webCrawl": "Web Crawl", "uploadDocument": "Upload Document", "completed": "Completed", "processing": "Processing", "systemHealthWarning": "System Health Warning", "systemHealthDescription": "Auto-refresh has been disabled due to system health issues. Please check system status in settings.", "documentsRange": "{{start}}-{{end}} of {{total}} documents", "clickOrDragToUpload": "Click or drag file to this area to upload", "supportedFormats": "Support for PDF, DOC, DOCX, TXT, and other document formats", "documentName": "Document Name", "url": "URL", "enterDocumentName": "Enter document name", "enterNewDocumentName": "Enter new document name", "parserConfiguration": "Parser Configuration (JSON)", "enterParserConfig": "Enter parser configuration in JSON format", "previewDocument": "Preview: {{name}}", "autoRefreshTooltip": "{{status}} auto-refresh during parsing", "enableAutoRefresh": "Enable", "disableAutoRefresh": "Disable", "webCrawlStarted": "Web crawl started successfully!", "webCrawlFailed": "Failed to start web crawl", "statusUpdated": "Document status updated successfully!", "statusUpdateFailed": "Failed to update document status", "parsingUpdated": "Document parsing updated successfully!", "parsingUpdateFailed": "Failed to update document parsing", "documentRenamed": "Document renamed successfully!", "renameFailed": "Failed to rename document", "parserChanged": "<PERSON><PERSON><PERSON> changed successfully!", "parserChangeFailed": "Failed to change parser", "uploadSuccess": "Document uploaded successfully!", "uploadFailed": "Failed to upload document", "selectDocumentsToParse": "Please select documents to parse", "selectDocumentsToDelete": "Please select documents to delete", "deleteDocuments": "Delete Documents", "deleteDocumentsConfirm": "Are you sure you want to delete {{count}} document(s)?", "deleteDocumentConfirm": "Are you sure you want to delete \"{{name}}\"? This action cannot be undone.", "deleteDocumentSuccess": "Document deleted successfully!", "deleteDocumentFailed": "Failed to delete document", "reparseWarning": "Re-parse will delete existing chunks. Continue?", "startParsing": "Start parsing?", "rename": "<PERSON><PERSON>", "changeParser": "Change Parser", "preview": "Preview", "download": "Download", "processBegin": "Process Begin", "duration": "Duration", "progress": "Progress", "message": "Message", "type": "Type", "size": "Size", "parsingStatus": "Parsing Status", "tokens": "Tokens", "parser": "<PERSON><PERSON><PERSON>", "thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "pleaseInputDocumentName": "Please input document name!", "pleaseInputURL": "Please input URL!", "pleaseInputValidURL": "Please input a valid URL!", "pleaseSelectParserType": "Please select parser type!", "general": "General", "qa": "Q&A", "resume": "Resume", "manual": "Manual", "table": "Table", "paper": "Paper", "book": "Book", "laws": "Laws", "presentation": "Presentation", "picture": "Picture", "one": "One", "audio": "Audio", "email": "Email", "tag": "Tag", "knowledgeGraph": "Knowledge Graph", "chunkTokens": "Chunk Tokens", "chunkTokensRequired": "Please enter chunk token number", "chunkTokensRange": "Must be between 1 and 2048", "chunkTokensTooltip": "Suggested token count threshold for generating text chunks. If the token count of a split text segment does not reach this threshold, it will continuously merge with subsequent text segments until merging the next text segment would exceed this threshold, at which point a final text chunk is generated. If the system never encounters text segmentation identifiers when splitting text segments, even if the text segment token count has exceeded this threshold, the system will not generate new text chunks.", "delimiter": "Delimiter", "delimiterRequired": "Please enter delimiter", "delimiterTooltip": "Supports multiple characters as delimiters, with multiple characters wrapped in two backticks \\`\\` delimiters. If configured as:\n`##`; the system will first use line breaks, two # symbols, and semicolons to split the text, then assemble the resulting small text chunks according to the 'Suggested Text Chunk Size' setting. Please ensure you understand the above text segmentation slicing mechanism before setting text segmentation identifiers.", "autoKeywords": "Auto Keywords", "autoKeywordsRange": "Must be between 0 and 30", "autoKeywordsTooltip": "Automatically extract N keywords for each chunk to increase their ranking for queries containing those keywords. Be aware that extra tokens will be consumed by the chat model specified in 'System model settings'. You can check or update the added keywords for a chunk from the chunk list.", "autoQuestions": "Auto Questions", "autoQuestionsRange": "Must be between 0 and 10", "autoQuestionsTooltip": "Automatically extract N questions for each chunk to increase their ranking for queries containing those questions. You can check or update the added questions for a chunk from the chunk list. This feature will not disrupt the chunking process if an error occurs, except that it may add an empty result to the original chunk. Be aware that extra tokens will be consumed by the LLM specified in 'System model settings'. ", "html4excel": "HTML4Excel", "html4excelTooltip": "Use with the General chunking method. When disabled, spreadsheets (XLSX or XLS(Excel 97-2003)) in the knowledge base will be parsed into key-value pairs. When enabled, they will be parsed into HTML tables, splitting every 12 rows if the original table has more than 12 rows.", "layoutRecognition": "Layout Recognition", "layoutRecognitionTooltip": "Enable layout recognition for better document parsing", "pageRank": "Page rank", "pageRankRange": "Must be between 0 and 100", "pageRankTooltip": "You can assign a higher PageRank score to specific knowledge bases during retrieval. The corresponding score is added to the hybrid similarity scores of retrieved chunks from these knowledge bases, increasing their ranking.", "raptor": "Use RAPTOR to enhance retrieval", "raptorTooltip": "Enable RAPTOR for multi-hop question-answering tasks.", "maxClusters": "Max Clusters", "maxClustersRange": "Must be between 1 and 256", "maxClustersTooltip": "Maximum number of clusters for RAPTOR", "threshold": "<PERSON><PERSON><PERSON><PERSON>", "thresholdRange": "Must be between 0 and 1", "thresholdTooltip": "In RAPTOR, chunks are clustered by their semantic similarity. The Threshold parameter sets the minimum similarity required for chunks to be grouped together. A higher Threshold means fewer chunks in each cluster, while a lower one means more.", "maxTokens": "<PERSON>", "maxTokensRange": "Must be between 0 and 2048", "maxTokensTooltip": "The maximum number of tokens per generated summary chunk for RAPTOR processing", "randomSeed": "Random Seed", "randomSeedRange": "Must be between 0 and 999999", "randomSeedTooltip": "Random seed for RAPTOR clustering reproducibility", "parserMethodTitle": "{{title}} Parser Method", "referenceNotAvailable": "Reference content not available", "referenceDataNotFound": "Reference data for {{index}} not found"}, "retrieval": {"title": "Retrieval Testing", "testQuestion": "Test Question", "retrievalSettings": "Retrieval Settings", "questionPlaceholder": "Enter your question to test retrieval...", "testRetrieval": "Test Retrieval", "enterQuestion": "Please enter a question", "foundChunks": "Found {{count}} relevant chunks (Total: {{total}})", "testFailed": "Retrieval test failed", "score": "Score", "keywords": "Keywords", "questions": "Questions", "results": "Results", "highlightOn": "Highlight ON", "totalResults": "Total {{total}} results", "noResults": "No results yet. Enter a question and click 'Test Retrieval' to start.", "configureParameters": "Configure retrieval parameters", "adjustParameters": "Adjust these parameters to fine-tune retrieval performance for your specific use case.", "similarityThreshold": "Similarity <PERSON><PERSON><PERSON><PERSON>", "similarityThresholdTooltip": "Minimum similarity score for results", "vectorSimilarityWeight": "Vector Similarity Weight", "vectorSimilarityWeightTooltip": "Weight for vector similarity in hybrid search", "topKResults": "Top K Results", "topKResultsTooltip": "Maximum number of results to return", "rerankModel": "<PERSON><PERSON>", "rerankModelTooltip": "Select rerank model for result reordering", "selectRerankModel": "Select rerank model", "noModelsAvailable": "No models available", "useKnowledgeGraph": "Use Knowledge Graph", "useKnowledgeGraphTooltip": "Enable knowledge graph for enhanced retrieval", "highlightKeywords": "Highlight Keywords", "highlightKeywordsTooltip": "Highlight matching keywords in results"}, "chunks": {"chunkIdRequired": "Chunk ID is required", "statusUpdated": "Chunk status updated successfully", "statusUpdateFailed": "Failed to update chunk status", "chunkIdMissing": "Chunk ID is missing", "content": "Content", "keywords": "Keywords", "status": "Status", "idMissing": "ID Missing", "enabled": "Enabled", "disabled": "Disabled", "idsRequired": "Knowledge Base ID and Document ID are required", "backToDocuments": "Back to Documents", "documentChunks": "Document Chunks", "viewAndManage": "View and manage chunks extracted from the document", "totalChunks": "Total Chunks", "searchPlaceholder": "Search chunks...", "chunksRange": "{{start}}-{{end}} of {{total}} chunks"}, "auth": {"signIn": "Sign In", "signUp": "Sign Up", "signOut": "Sign Out", "email": "Email", "password": "Password", "nickname": "Nickname", "rememberMe": "Remember me", "createAccount": "Create Account", "welcomeBack": "Welcome back to HanBangGaoKe Knowledge Base System", "createAccountDesc": "Create your account to get started", "dontHaveAccount": "Don't have an account?", "alreadyHaveAccount": "Already have an account?", "enterEmail": "Enter your email", "enterPassword": "Enter your password", "enterNickname": "Enter your nickname", "emailRequired": "Please input your email!", "passwordRequired": "Please input your password!", "nicknameRequired": "Please input your nickname!", "emailInvalid": "Please enter a valid email!", "orContinueWith": "Or continue with", "signInWith": "Sign in with {{provider}}"}, "navigation": {"dashboard": "Dashboard", "knowledgeBase": "Knowledge Base Settings", "aiRead": "AI Read", "textToImage": "AI Text to Image", "documents": "Knowledge Base Documents", "conversations": "Knowledge Base Conversations", "aiModels": "AI Model Settings", "settings": "Personal Settings", "dialogs": "Knowledge Base Dialog Config", "chat": "Cha<PERSON>", "chatbot": "AI Assistant"}, "dashboard": {"title": "Dashboard", "welcome": "Welcome to HanBangGaoKe", "overview": "Overview", "statistics": "Statistics", "recentActivity": "Recent Activity", "welcomeBack": "Welcome back, {{name}}!", "manageDescription": "Manage your knowledge base and interact with AI-powered insights", "knowledgeBases": "Knowledge Bases", "quickActions": "Quick Actions", "recentActivities": "Recent Activities", "createKnowledgeBase": "Create Knowledge Base", "createKnowledgeBaseDesc": "Start building your knowledge repository", "manageDocuments": "Manage Documents", "manageDocumentsDesc": "Upload and organize your documents", "startConversation": "Start Conversation", "startConversationDesc": "Chat with your AI assistant", "configureModels": "Configure Models", "configureModelsDesc": "Set up your AI models", "knowledgeBaseCreated": "Knowledge Base created", "documentUploaded": "Document uploaded", "conversationCompleted": "Conversation with AI completed", "modelConfigUpdated": "Model configuration updated", "activityTypes": {"create": "Create", "upload": "Upload", "chat": "Cha<PERSON>", "config": "Config"}}, "dialog": {"title": "Dialogs", "createDialog": "Create Dialog", "dialogName": "Dialog Name", "language": "Language", "description": "Description", "enterDialogName": "Enter dialog name", "selectLanguage": "Select language", "enterDescription": "Enter description (optional)", "nameRequired": "Please enter dialog name", "languageRequired": "Please select language", "descriptionMaxLength": "Description cannot exceed 500 characters", "createSuccess": "Dialog created successfully", "deleteConfirm": "Are you sure you want to delete this dialog?", "deleteSuccess": "Dialog deleted successfully", "assistantName": "Assistant Name", "enterAssistantName": "Enter assistant name", "assistantNameRequired": "Please enter assistant name", "systemPrompt": "System Prompt", "systemPromptTooltip": "System instructions that define the assistant's behavior", "enterSystemPrompt": "Enter system prompt to define the assistant's behavior...", "prologue": "Prologue", "prologueTooltip": "Opening message shown to users", "enterPrologue": "Enter prologue message (optional)", "emptyResponse": "Empty Response", "emptyResponseTooltip": "Response when no relevant information is found", "emptyResponsePlaceholder": "Sorry, I don't know.", "setOpener": "Set An Opener", "setOpenerTooltip": "Opening message shown to users", "defaultOpener": "Hi! I'm your assistant, how can I help you?", "basicInformation": "Basic Information", "selectKnowledgeBases": "Select Knowledge Bases", "selectKnowledgeBasesTooltip": "Choose knowledge bases to use for this dialog", "editDialog": "Edit Dialog", "startChat": "Start Chat", "manageDescription": "Manage your chat dialogs and configurations", "searchPlaceholder": "Search dialogs...", "createFirstDialog": "Create Your First Dialog", "selectDialogsToDelete": "Please select dialogs to delete", "deleteDialogs": "Delete Dialogs", "batchDeleteConfirm": "Are you sure you want to delete {{count}} dialog(s)?", "knowledgeBases": "Knowledge Bases", "noKnowledgeBases": "No knowledge bases", "notSet": "Not set", "invalidDialogId": "Invalid Dialog ID", "assistantAvatar": "Assistant <PERSON><PERSON>", "quote": "Quote", "quoteTooltip": "Enable quote in responses", "keyword": "Keyword", "keywordTooltip": "Enable keyword extraction", "tts": "TTS", "ttsTooltip": "Enable text-to-speech", "knowledgeBasesTooltip": "Select knowledge bases for this dialog", "upload": "Upload", "enterVariableKey": "Enter variable key", "optional": "Optional", "variable": "Variable", "system": "System", "systemPromptRequired": "System prompt is required", "similarityThreshold": "Similarity <PERSON><PERSON><PERSON><PERSON>", "similarityThresholdTooltip": "Minimum similarity score for retrieval results", "vectorSimilarityWeight": "Vector Similarity Weight", "vectorSimilarityWeightTooltip": "Weight for vector similarity in hybrid search", "topN": "Top N", "topNTooltip": "Number of documents to retrieve", "multiTurn": "Multi Turn", "multiTurnTooltip": "Enable multi-turn conversation", "reasoning": "Reasoning", "reasoningTooltip": "Enable reasoning mode", "rerankModel": "<PERSON><PERSON>", "rerankModelTooltip": "Select rerank model for result reordering", "selectRerankModel": "Select rerank model", "topK": "Top K", "topKTooltip": "Number of documents to rerank", "variableTooltip": "Define custom variables for the prompt", "defaultSystemPrompt": "You are an intelligent assistant. Please answer questions based on your knowledge and training. Provide helpful, accurate, and detailed responses to user queries.", "defaultSystemPromptWithKB": "You are an intelligent assistant. Please summarize the content of the knowledge base to answer the question. Please list the data in the knowledge base and answer in detail. When all knowledge base content is irrelevant to the question, your answer must include the sentence \"The answer you are looking for is not found in the knowledge base!\" Answers need to consider chat history.\nHere is the knowledge base:\n{knowledge}\nThe above is the knowledge base.", "noDialogIdProvided": "No dialog ID provided in the URL", "goToDialogs": "Go to Dialogs", "backToDialogs": "Back to Dialogs", "deleteSelected": "Delete Selected", "dialogsRange": "{{start}}-{{end}} of {{total}} dialogs", "llmModel": "LLM Model", "llmModelTooltip": "Choose the language model for this dialog", "llmModelRequired": "Please select LLM model", "selectLLMModel": "Select LLM model", "modelParameters": "Model Parameters", "temperature": "Temperature", "temperatureTooltip": "Controls randomness in responses (0-1)", "topP": "Top P", "topPTooltip": "Controls diversity via nucleus sampling", "presencePenalty": "Presence Penalty", "presencePenaltyTooltip": "Penalty for repeating topics", "frequencyPenalty": "Frequency Penalty", "frequencyPenaltyTooltip": "Penalty for repeating tokens", "maxTokens": "<PERSON>", "maxTokensTooltip": "Maximum number of tokens in response", "assistantSettings": "Assistant Setting", "promptEngine": "Prompt Engine", "modelSettings": "Model Setting"}, "chat": {"title": "Cha<PERSON>", "sendMessage": "Send message (Enter)", "stopGenerating": "Stop generating", "uploadFile": "Upload file", "voiceInput": "Voice input", "enterMessage": "Enter your message...", "noMessages": "No messages yet", "copySuccess": "<PERSON><PERSON>d successfully", "copyFailed": "Copy failed, please copy manually", "welcome": "Welcome to {{name}}", "defaultWelcome": "Hi! I'm your assistant, how can I help you?", "noDialogSelected": "No Dialog Selected", "selectDialogDesc": "Please select a dialog from the dialogs page to start chatting.", "goToDialogs": "Go to Dialogs", "dialogNotFound": "Dialog Not Found", "backToConversations": "Back to Conversations", "chatWithDialog": "Chat with {{name}}...", "configureLLMFirst": "Please configure LLM model first", "welcomeTo": "Welcome to {{name}}", "model": "Model", "notConfigured": "Not configured", "knowledgeBases": "Knowledge Bases", "connected": "{{count}} connected", "prologue": "Prologue", "parameters": "Parameters", "noPromptConfiguration": "No prompt configuration"}, "conversations": {"title": "Conversations", "searchConversations": "Search conversations...", "noConversations": "No conversations found", "viewConversation": "View", "continueChat": "Continue", "deleteConversation": "Delete", "deleteConfirm": "Are you sure you want to delete this conversation?", "deleteSuccess": "Conversation deleted successfully", "userMessages": "{{count}} user messages", "aiMessages": "{{count}} AI messages", "lastMessage": "Last message", "by": "by", "manageDescription": "Manage your chat conversations and history", "newConversation": "New Conversation", "totalConversations": "Total Conversations", "withMessages": "With Messages", "totalMessages": "Total Messages", "recentlyActive": "Recently Active", "loading": "Loading conversations...", "noConversationsFound": "No conversations found for \"{{keywords}}\"", "startNewConversation": "Start New Conversation", "chooseDialogAndStart": "Choose Dialog & Start Chat", "selectDialogToStart": "Select a Dialog to Start Conversation", "searchDialogsPlaceholder": "Search dialogs by name or description...", "noDialogsFoundFor": "No dialogs found for \"{{keywords}}\"", "noDialogsAvailable": "No dialogs available", "createFirstDialog": "Create Your First Dialog", "updated": "Updated", "manageDialogs": "Manage Dialogs"}, "aiModels": {"title": "AI Models", "availableModels": "Available Models", "myModels": "My Models", "addModel": "Add Model", "configureDialog": "Configure <PERSON>", "modelName": "Model Name", "provider": "Provider", "status": "Status", "actions": "Actions", "active": "Active", "inactive": "Inactive", "configure": "Configure", "remove": "Remove", "apiKey": "API Key", "baseUrl": "Base URL", "enterApiKey": "Please input API Key!", "enterBaseUrl": "Please input Base URL!", "apiKeyTooltip": "API key for authentication", "baseUrlTooltip": "Base URL for the API endpoint", "configureSuccess": "Model configured successfully", "removeConfirm": "Are you sure you want to remove this model?", "removeSuccess": "Model removed successfully", "modelSettings": "Model Settings", "configureFailed": "Failed to configure API Key", "addFailed": "Failed to add model", "manageDescription": "Manage your AI model providers and configurations", "configureFactory": "Configure {{factory}}", "apiKeyPlaceholder": "Enter API key (optional for local models)", "availableProviders": "Available Providers", "setDefaultModels": "Set Default Models", "setDefaultSuccess": "Default models set successfully!", "setDefaultFailed": "Failed to set default models"}, "settings": {"title": "Settings", "profile": "Profile", "preferences": "Preferences", "security": "Security", "about": "About", "userInfo": "User Information", "changePassword": "Change Password", "currentPassword": "Current Password", "newPassword": "New Password", "confirmPassword": "Confirm Password", "updateProfile": "Update Profile", "updateSuccess": "Settings updated successfully", "manageDescription": "Manage your account settings and system information", "profileSettings": "Profile Settings", "passwordSettings": "Password Settings", "updateFailed": "Failed to update profile", "passwordUpdateFailed": "Failed to update password", "changeAvatar": "Change Avatar", "nicknamePlaceholder": "Enter your nickname", "emailRequired": "Please input your email!", "emailInvalid": "Please enter a valid email!", "emailPlaceholder": "Enter your email", "currentPasswordRequired": "Please input your current password!", "currentPasswordPlaceholder": "Enter current password", "newPasswordRequired": "Please input your new password!", "passwordMinLength": "Password must be at least 6 characters!", "newPasswordPlaceholder": "Enter new password", "confirmPasswordRequired": "Please confirm your new password!", "passwordMismatch": "Passwords do not match!", "confirmPasswordPlaceholder": "Confirm new password", "updatePassword": "Update Password", "systemInformation": "System Information", "versionInformation": "Version Information", "version": "Version", "userId": "User ID", "created": "Created", "lastUpdated": "Last Updated", "systemStatus": "System Status"}, "messages": {"success": {"created": "Created successfully", "updated": "Updated successfully", "deleted": "Deleted successfully", "saved": "Saved successfully", "uploaded": "Uploaded successfully", "copied": "<PERSON><PERSON>d successfully", "loginSuccess": "Login successful!"}, "error": {"createFailed": "Create failed", "updateFailed": "Update failed", "deleteFailed": "Delete failed", "saveFailed": "Save failed", "uploadFailed": "Upload failed", "copyFailed": "Co<PERSON> failed", "networkError": "Network connection error, please check your network", "networkErrorTitle": "Network Error", "requestError": "Request Error {{status}}: {{url}}", "unknownError": "Unknown error"}, "validation": {"required": "This field is required", "emailInvalid": "Please enter a valid email", "passwordTooShort": "Password must be at least 6 characters", "passwordMismatch": "Passwords do not match", "maxLength": "Maximum {{max}} characters allowed"}, "warning": {"enterQuestion": "Please enter a question to test"}}, "textToImage": {"title": "AI Text to Image", "description": "AI-powered text-to-image service, input text description to generate high-quality images", "loading": "Loading text-to-image service...", "loadError": "Unable to load text-to-image service, please check if the service is running normally", "serviceUnavailable": "Service loading failed"}, "aiRead": {"title": "AI Read", "uploadDocument": "Upload Document", "documentContent": "Document Content", "preview": "Preview", "summary": "Summary", "pending": "Pending", "chat": "Cha<PERSON>", "uploadArea": "Click or drag file to this area to upload", "uploadHint": "Support for PDF, DOC, DOCX, TXT, and other document formats", "documentSummary": "Document Summary", "askQuestion": "Ask about this document", "generateSummary": "Generate Summary", "summaryPrompt": "Please summarize the main content of this document, including key points, conclusions, and important information.", "noDocumentUploaded": "No document uploaded yet", "uploadFirst": "Please upload a document first", "summaryGenerating": "Generating summary...", "summaryGenerated": "Summary generated successfully", "askAboutDocument": "Ask questions about the uploaded document", "typeQuestion": "Type your question about the document...", "documentQA": "Document Q&A", "uploadedFiles": "Uploaded Files", "maxFiles": "Max 25 files, 50MB each", "noDocumentSelected": "No document selected", "clickToView": "Click on a file above to view its content", "uploadToStart": "Upload files above to get started", "uploadMore": "Upload More", "fileUploaded": "file(s) uploaded successfully", "maxFilesError": "Maximum 25 files allowed, please delete some files first.", "unsupportedFormat": "Unsupported file format: {{filename}}. Please upload PDF, DOC, DOCX, PPT, PPTX, TXT or MD files.", "fileTooLarge": "File {{filename}} is too large, maximum {{maxSize}}MB supported.", "uploadFailed": "Document upload failed", "uploadFailedError": "Document upload failed:", "fileSize": "File Size:", "fileType": "File Type:", "processingStatus": "Processing Status:", "processingProgress": "Processing Progress:", "aiGeneratedSummary": "AI Generated Content Summary", "selectFileToView": "Please select a file to view details", "startProcessing": "Start Processing", "refreshStatus": "Refresh Status", "deleteFile": "Delete File", "confirmDelete": "Confirm Delete", "confirmDeleteMessage": "Are you sure you want to delete file \"{{filename}}\"? This action cannot be undone.", "description": "Upload documents and get AI-powered insights through summarization and Q&A", "status": {"uploaded": "Uploaded", "converting_to_pdf": "Converting to PDF", "calling_mineru": "Calling Parse API", "processing": "Processing", "saving_results": "Saving Results", "generating_summary": "Generating Summary", "completed": "Completed", "failed": "Failed"}}, "chatbot": {"title": "AI Assistant", "subtitle": "Intelligent Chat Assistant", "description": "HanBangGaoKe-powered intelligent chatbot", "initializing": "Initializing...", "noMessages": "No messages yet", "thinking": "Thinking...", "inputPlaceholder": "Type your message...", "send": "Send", "stop": "Stop", "clear": "Clear Chat", "reset": "Reset Session", "settings": "Settings", "streamMode": "Stream Mode", "normalMode": "Normal Mode", "systemPrompt": "System Prompt", "sessionInfo": "Session Info", "messageCount": "Message Count", "sessionId": "Session ID", "createdAt": "Created At", "lastActive": "Last Active", "defaultSystemPrompt": "You are HanBangGaoKe's AI assistant, specialized in helping users with the HanBangGaoKe system. Please answer questions in Chinese and maintain a friendly and professional tone.", "error": {"sessionNotFound": "Session not found", "messageEmpty": "Message cannot be empty", "sendFailed": "Failed to send message", "loadFailed": "Failed to load", "networkError": "Network error", "serverError": "Server error", "unauthorized": "Unauthorized access"}, "success": {"sessionCreated": "Session created successfully", "sessionCleared": "Session cleared", "sessionReset": "Session reset", "messageSent": "Message sent successfully"}, "tips": {"enterToSend": "Press Enter to send, Shift+Enter for new line", "streamingMode": "Stream mode shows real-time responses", "clearWarning": "Chat history will be permanently deleted", "resetWarning": "A new session will be created"}, "features": "Features"}, "documents": {"loadingDocument": "Loading document...", "noDocumentSelected": "No document selected", "pdfPreviewError": "PDF Preview Error", "pdfNotAvailable": "PDF Not Available", "unableToLoadPdf": "Unable to load PDF file for preview"}}